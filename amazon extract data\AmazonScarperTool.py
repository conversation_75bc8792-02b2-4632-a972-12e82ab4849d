from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import pandas as pd
import time
url = input("Enter the Amazon URL: ")
# Configure Selenium options
options = webdriver.ChromeOptions()
options.add_argument("--disable-blink-features=AutomationControlled")
options.add_argument("--no-sandbox")
options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

# Initialize the WebDriver
driver = webdriver.Chrome(
    service=Service(ChromeDriverManager().install()),
    options=options
)



try:
    driver.get(url)
    time.sleep(3)  # Wait for page to load

    # Wait for product elements to load
    WebDriverWait(driver, 15).until(
        EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div[data-component-type='s-search-result']"))
    )

    # Lists to store data
    product_names = []
    product_links = []
    product_ratings = []
    product_prices = []
    product_descriptions = []
    product_stock = []

    # Find all product containers
    products = driver.find_elements(By.CSS_SELECTOR, "div[data-component-type='s-search-result']")

    for product in products:
        # Extract product name
        try:
            name_element = product.find_element(By.CSS_SELECTOR, "a.a-link-normal.s-line-clamp-4.s-link-style.a-text-normal span")
            product_names.append(name_element.text.strip())
        except:
            product_names.append("N/A")

        # Extract product link
        try:
            link_element = product.find_element(By.CSS_SELECTOR, "a.a-link-normal.s-line-clamp-4.s-link-style.a-text-normal")
            product_links.append(link_element.get_attribute("href"))
        except:
            product_links.append("N/A")

        # Extract product price
        try:
            price_whole = product.find_element(By.CSS_SELECTOR, "span.a-price-whole").text
            price_fraction = product.find_element(By.CSS_SELECTOR, "span.a-price-fraction").text
            product_prices.append(f"{price_whole}.{price_fraction}")
        except:
            product_prices.append("N/A")

        # Extract product description
        try:
            description = product.find_elements(By.CSS_SELECTOR, "div.a-color-secondary")
            if description:
                desc_text = [elem.text.strip() for elem in description[0].find_elements(By.CSS_SELECTOR, "span")]
                product_descriptions.append(" | ".join(desc_text))
            else:
                product_descriptions.append("N/A")
        except:
            product_descriptions.append("N/A")

        # Extract product rating
        try:
            rating = product.find_elements(By.CSS_SELECTOR, "span.a-icon-alt")
            product_ratings.append(rating[0].get_attribute("innerHTML") if rating else "N/A")
        except:
            product_ratings.append("N/A")

        # Stock status - Requires visiting each product's detail page (time-consuming)
       

    # Create DataFrame
    df = pd.DataFrame({
        "Product Name": product_names,
        "Product Link": product_links,
        "Rating": product_ratings,
        "Price": product_prices,
        "Description": product_descriptions,
        
    })

    # Save to CSV
    df.to_csv("amazon_product_details.csv", index=False)
    print("Data saved to amazon_product_details.csv!")

except Exception as e:
    print(f"Error: {e}")

finally:
    driver.quit()
