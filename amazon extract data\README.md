# Amazon Product Scraper Tool

A Python-based web scraper that extracts product information from Amazon search results using Selenium WebDriver.

## Features

- **Product Information Extraction**: Scrapes product names, links, ratings, prices, and descriptions
- **CSV Export**: Automatically saves extracted data to a CSV file
- **Anti-Detection**: Configured with user-agent and Chrome options to avoid detection
- **Error Handling**: Robust error handling for missing product elements
- **Wait Conditions**: Uses WebDriverWait for reliable element loading

## Prerequisites

Before running this tool, make sure you have the following installed:

- Python 3.7 or higher
- Google Chrome browser

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd amazon-scraper-tool
```

2. Install required dependencies:
```bash
pip install selenium pandas webdriver-manager
```

## Usage

1. Run the script:
```bash
python AmazonScarperTool.py
```

2. When prompted, enter the Amazon search URL:
```
Enter the Amazon URL: https://www.amazon.com/s?k=your-search-term
```

3. The script will:
   - Open a Chrome browser window
   - Navigate to the provided URL
   - Extract product information from search results
   - Save the data to `amazon_product_details.csv`

## Output

The tool generates a CSV file (`amazon_product_details.csv`) with the following columns:

- **Product Name**: The title of the product
- **Product Link**: Direct URL to the product page
- **Rating**: Customer rating information
- **Price**: Product price (whole and fractional parts)
- **Description**: Additional product details

## Technical Details

### Dependencies

- `selenium`: Web automation framework
- `pandas`: Data manipulation and CSV export
- `webdriver-manager`: Automatic Chrome driver management

### Chrome Options

The script uses several Chrome options to avoid detection:
- Disables automation control features
- Sets custom user-agent
- Disables sandbox mode

### Selectors Used

- Product containers: `div[data-component-type='s-search-result']`
- Product names: `a.a-link-normal.s-line-clamp-4.s-link-style.a-text-normal span`
- Product links: `a.a-link-normal.s-line-clamp-4.s-link-style.a-text-normal`
- Prices: `span.a-price-whole` and `span.a-price-fraction`
- Descriptions: `div.a-color-secondary`
- Ratings: `span.a-icon-alt`

## Important Notes

⚠️ **Legal Disclaimer**: This tool is for educational purposes only. Please ensure you comply with Amazon's Terms of Service and robots.txt file when using this scraper.

⚠️ **Rate Limiting**: Be respectful of Amazon's servers. The script includes delays to avoid overwhelming the website.

⚠️ **Selector Changes**: Amazon may change their HTML structure, which could break the selectors used in this script.

## Troubleshooting

### Common Issues

1. **Chrome driver not found**: The script uses `webdriver-manager` to automatically download the correct Chrome driver version.

2. **Elements not found**: Amazon's page structure may have changed. Check the browser console for the current selectors.

3. **Access denied**: Amazon may block automated requests. Try:
   - Adding longer delays between requests
   - Using different user-agent strings
   - Running the script less frequently

### Error Handling

The script includes try-catch blocks for each data extraction operation, defaulting to "N/A" when elements are not found.

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is open source and available under the [MIT License](LICENSE).

## Author

Created for educational and research purposes.
